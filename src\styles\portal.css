/* Portal Mode Styles */
@import url('https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap');

* {
  box-sizing: border-box;
}

body {
  margin: 0;
  padding: 0;
  font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', sans-serif;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  min-height: 100vh;
  overflow-x: hidden;
}

#portal-root {
  width: 100%;
  height: 100vh;
  display: flex;
  flex-direction: column;
}

/* Portal Container */
.portal-container {
  width: 100%;
  height: 100vh;
  display: flex;
  flex-direction: column;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  animation: fadeIn 0.6s ease-out;
}

@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* Portal Header */
.portal-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 2rem 2rem 1rem 2rem;
  background: rgba(255, 255, 255, 0.1);
  backdrop-filter: blur(10px);
  border-bottom: 1px solid rgba(255, 255, 255, 0.2);
  animation: slideDown 0.8s ease-out;
}

@keyframes slideDown {
  from {
    transform: translateY(-100%);
    opacity: 0;
  }
  to {
    transform: translateY(0);
    opacity: 1;
  }
}

.portal-branding {
  display: flex;
  align-items: center;
  gap: 1rem;
}

.portal-logo {
  font-size: 3rem;
  animation: bounce 2s infinite;
}

@keyframes bounce {
  0%, 20%, 50%, 80%, 100% {
    transform: translateY(0);
  }
  40% {
    transform: translateY(-10px);
  }
  60% {
    transform: translateY(-5px);
  }
}

.portal-title h1 {
  margin: 0;
  font-size: 2rem;
  font-weight: 700;
  color: white;
  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
}

.portal-title p {
  margin: 0.25rem 0 0 0;
  font-size: 1rem;
  color: rgba(255, 255, 255, 0.9);
  font-weight: 400;
}

/* Connection Status */
.portal-status {
  display: flex;
  align-items: center;
}

.connection-status {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  padding: 0.5rem 1rem;
  border-radius: 20px;
  font-size: 0.875rem;
  font-weight: 500;
  transition: all 0.3s ease;
}

.connection-status.online {
  background: rgba(34, 197, 94, 0.2);
  color: #22c55e;
  border: 1px solid rgba(34, 197, 94, 0.3);
}

.connection-status.offline {
  background: rgba(239, 68, 68, 0.2);
  color: #ef4444;
  border: 1px solid rgba(239, 68, 68, 0.3);
}

.status-indicator {
  width: 8px;
  height: 8px;
  border-radius: 50%;
  animation: pulse 2s infinite;
}

.connection-status.online .status-indicator {
  background: #22c55e;
}

.connection-status.offline .status-indicator {
  background: #ef4444;
}

@keyframes pulse {
  0% {
    transform: scale(0.95);
    box-shadow: 0 0 0 0 rgba(34, 197, 94, 0.7);
  }
  70% {
    transform: scale(1);
    box-shadow: 0 0 0 10px rgba(34, 197, 94, 0);
  }
  100% {
    transform: scale(0.95);
    box-shadow: 0 0 0 0 rgba(34, 197, 94, 0);
  }
}

/* Portal Chat Area */
.portal-chat-area {
  flex: 1;
  display: flex;
  flex-direction: column;
  max-width: 1200px;
  width: 100%;
  margin: 0 auto;
  background: white;
  border-radius: 20px 20px 0 0;
  box-shadow: 0 -10px 30px rgba(0, 0, 0, 0.2);
  overflow: hidden;
  animation: slideUp 0.8s ease-out 0.2s both;
}

@keyframes slideUp {
  from {
    transform: translateY(100%);
    opacity: 0;
  }
  to {
    transform: translateY(0);
    opacity: 1;
  }
}

/* Error and Warning Banners */
.portal-error-banner,
.portal-warning-banner {
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 1rem;
  margin: 0.5rem 1rem;
  border-radius: 12px;
  animation: slideInFromTop 0.5s ease-out;
}

.portal-error-banner {
  background: linear-gradient(135deg, #fee2e2 0%, #fecaca 100%);
  border: 1px solid #f87171;
  color: #dc2626;
}

.portal-warning-banner {
  background: linear-gradient(135deg, #fef3c7 0%, #fde68a 100%);
  border: 1px solid #f59e0b;
  color: #d97706;
}

@keyframes slideInFromTop {
  from {
    transform: translateY(-100%);
    opacity: 0;
  }
  to {
    transform: translateY(0);
    opacity: 1;
  }
}

.error-content {
  display: flex;
  align-items: center;
  gap: 0.75rem;
}

.error-icon,
.warning-icon {
  font-size: 1.25rem;
}

.error-message,
.warning-message {
  flex: 1;
  font-weight: 500;
}

.retry-btn {
  background: #dc2626;
  color: white;
  border: none;
  padding: 0.5rem 1rem;
  border-radius: 8px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.3s ease;
}

.retry-btn:hover {
  background: #b91c1c;
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(220, 38, 38, 0.3);
}

/* Portal Error Page */
.portal-error {
  display: flex;
  align-items: center;
  justify-content: center;
  height: 100vh;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
}

.error-content {
  text-align: center;
  background: white;
  padding: 3rem;
  border-radius: 20px;
  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.2);
  max-width: 500px;
  animation: bounceIn 0.8s ease-out;
}

@keyframes bounceIn {
  0% {
    transform: scale(0.3);
    opacity: 0;
  }
  50% {
    transform: scale(1.05);
  }
  70% {
    transform: scale(0.9);
  }
  100% {
    transform: scale(1);
    opacity: 1;
  }
}

.error-content .error-icon {
  font-size: 4rem;
  margin-bottom: 1rem;
}

.error-content h1 {
  color: #374151;
  margin-bottom: 1rem;
  font-size: 1.5rem;
}

.error-content p {
  color: #6b7280;
  margin-bottom: 1rem;
  line-height: 1.6;
}

.retry-button {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  border: none;
  padding: 0.75rem 2rem;
  border-radius: 12px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
  font-size: 1rem;
}

.retry-button:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 20px rgba(102, 126, 234, 0.4);
}

/* Mobile-First Responsive Design */
@media (max-width: 768px) {
  .portal-header {
    padding: 1rem;
    flex-direction: column;
    gap: 1rem;
    text-align: center;
  }

  .portal-title h1 {
    font-size: 1.5rem;
  }

  .portal-chat-area {
    border-radius: 15px 15px 0 0;
  }

  .error-content {
    margin: 1rem;
    padding: 2rem;
  }

  /* Mobile chat input optimizations */
  .chat-input.portal-mode {
    padding: 1rem;
    /* Add safe area for devices with notches */
    padding-bottom: calc(1rem + env(safe-area-inset-bottom));
    overflow: hidden;
  }

  .chat-input.portal-mode .input-row {
    gap: 0.5rem;
    max-width: 100%;
  }

  .chat-input.portal-mode .portal-textarea {
    font-size: 16px; /* Prevents zoom on iOS */
    padding: 0.75rem;
    max-height: 100px; /* Smaller max height on mobile */
  }

  .chat-input.portal-mode .portal-input {
    font-size: 16px; /* Prevents zoom on iOS */
    padding: 0.75rem;
  }

  .chat-input.portal-mode .icon-button {
    padding: 0.625rem;
    font-size: 1.125rem;
    min-width: 40px;
    min-height: 40px;
    flex-shrink: 0; /* Prevent buttons from shrinking */
  }

  /* Larger touch targets for mobile */
  .chat-messages.portal-mode {
    padding: 1rem;
    /* Add safe area for devices with notches */
    padding-bottom: calc(1rem + env(safe-area-inset-bottom));
  }
}

@media (max-width: 480px) {
  .portal-header {
    padding: 0.75rem;
  }

  .portal-title h1 {
    font-size: 1.25rem;
  }

  .portal-title p {
    font-size: 0.875rem;
  }

  .portal-logo {
    font-size: 2rem;
  }

  /* Extra small mobile optimizations */
  .chat-input.portal-mode {
    padding: 0.75rem;
    padding-bottom: calc(0.75rem + env(safe-area-inset-bottom));
    overflow: hidden;
  }

  .chat-input.portal-mode .input-row {
    gap: 0.375rem;
    max-width: 100%;
  }

  .chat-input.portal-mode .portal-textarea {
    font-size: 16px; /* Prevents zoom on iOS */
    padding: 0.625rem 0.75rem;
    border-radius: 10px;
    max-height: 80px; /* Even smaller max height on very small screens */
  }

  .chat-input.portal-mode .portal-input {
    font-size: 16px; /* Prevents zoom on iOS */
    padding: 0.625rem 0.75rem;
    border-radius: 10px;
  }

  .chat-input.portal-mode .icon-button {
    padding: 0.5rem;
    font-size: 1rem;
    min-width: 36px;
    min-height: 36px;
    border-radius: 8px;
    flex-shrink: 0; /* Prevent buttons from shrinking */
  }

  .chat-input.portal-mode .portal-buttons {
    gap: 0.25rem;
    flex-shrink: 0; /* Prevent button container from shrinking */
  }

  /* Optimize message area for small screens */
  .chat-messages.portal-mode {
    padding: 0.75rem;
  }

  /* Make header more compact on very small screens */
  .chat-header.portal-mode {
    padding: 0.75rem 1rem;
    font-size: 1rem;
  }
}

/* Portal Mode Overrides for Chat Components */

/* Hide the floating chat button in portal mode */
.portal-container .chat-button {
  display: none !important;
}

/* Make chat container always visible and full-screen */
.portal-container .chat-container {
  position: static !important;
  width: 100% !important;
  height: 100% !important;
  max-width: none !important;
  max-height: none !important;
  border-radius: 0 !important;
  box-shadow: none !important;
  bottom: auto !important;
  right: auto !important;
  transform: none !important;
  display: flex !important;
  flex-direction: column;
  flex: 1;
}

/* Ensure chat messages area takes full height in portal mode */
.portal-container .chat-container.portal-mode {
  height: 100%;
}

.portal-container .chat-container.portal-mode .chat-messages {
  flex: 1;
  max-height: none;
  overflow-y: auto;
}

.chat-header.portal-mode {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  border-radius: 0;
  font-size: 1.1rem;
  padding: 1rem 1.5rem;
}

.chat-messages.portal-mode {
  flex: 1;
  max-height: none;
  padding: 1.5rem;
  background: #f8fafc;
}

/* Portal Mode Chat Input - Horizontal Layout */
.chat-input.portal-mode {
  padding: 1rem 1.5rem;
  background: white;
  border-top: 1px solid #e2e8f0;
  border-radius: 0;
  box-sizing: border-box;
  width: 100%;
  overflow: hidden; /* Prevent horizontal overflow */
}

/* Horizontal input row for portal mode */
.chat-input.portal-mode .input-row {
  display: flex;
  align-items: flex-end; /* Align to bottom for multi-line textarea */
  gap: 0.75rem;
  width: 100%;
  max-width: 100%;
  box-sizing: border-box;
}

/* Portal textarea takes most of the space with auto-expanding */
.chat-input.portal-mode .portal-textarea {
  flex: 1;
  font-size: 1rem;
  padding: 0.875rem 1rem;
  border-radius: 12px;
  border: 2px solid #e2e8f0;
  transition: all 0.3s ease;
  background: #f8fafc;
  min-height: 44px; /* Minimum touch target for mobile */
  max-height: 120px; /* Limit maximum height */
  line-height: 1.4;
  font-family: inherit;
  resize: none;
  overflow: hidden;
  box-sizing: border-box;
  width: 0; /* Allow flex to control width */
}

.chat-input.portal-mode .portal-textarea:focus {
  border-color: #667eea;
  box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
  background: white;
  outline: none;
}

/* Legacy support for portal-input class (fallback) */
.chat-input.portal-mode .portal-input {
  flex: 1;
  font-size: 1rem;
  padding: 0.875rem 1rem;
  border-radius: 12px;
  border: 2px solid #e2e8f0;
  transition: all 0.3s ease;
  background: #f8fafc;
  min-height: 44px;
  box-sizing: border-box;
  width: 0; /* Allow flex to control width */
}

.chat-input.portal-mode .portal-input:focus {
  border-color: #667eea;
  box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
  background: white;
}

/* Portal buttons container */
.chat-input.portal-mode .portal-buttons {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  flex-shrink: 0;
  flex-wrap: nowrap; /* Prevent wrapping */
  min-width: fit-content;
}

/* Portal mode icon buttons */
.chat-input.portal-mode .icon-button {
  padding: 0.75rem;
  font-size: 1.25rem;
  border-radius: 10px;
  transition: all 0.3s ease;
  min-width: 44px; /* Minimum touch target for mobile */
  min-height: 44px;
  display: flex;
  align-items: center;
  justify-content: center;
  background: #f1f5f9;
  border: 1px solid #e2e8f0;
}

.chat-input.portal-mode .icon-button:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
  background: #e2e8f0;
}

.chat-input.portal-mode .send-button {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  border: none;
}

.chat-input.portal-mode .send-button:hover {
  background: linear-gradient(135deg, #5a67d8 0%, #6b46c1 100%);
}

.chat-input.portal-mode .send-button:disabled {
  background: #cbd5e1;
  color: #64748b;
  cursor: not-allowed;
  transform: none;
  box-shadow: none;
}

/* Attach and camera button styling */
.chat-input.portal-mode .attach-button:hover {
  background: #dbeafe;
  border-color: #3b82f6;
}

.chat-input.portal-mode .camera-button:hover {
  background: #dcfce7;
  border-color: #22c55e;
}

/* Mobile-specific touch optimizations */
@media (hover: none) and (pointer: coarse) {
  /* Remove hover effects on touch devices */
  .chat-input.portal-mode .icon-button:hover {
    transform: none;
    box-shadow: none;
  }

  /* Add active states for better touch feedback */
  .chat-input.portal-mode .icon-button:active {
    transform: scale(0.95);
    background: #e2e8f0;
  }

  .chat-input.portal-mode .send-button:active {
    transform: scale(0.95);
    background: linear-gradient(135deg, #5a67d8 0%, #6b46c1 100%);
  }

  .chat-input.portal-mode .attach-button:active {
    background: #dbeafe;
    border-color: #3b82f6;
  }

  .chat-input.portal-mode .camera-button:active {
    background: #dcfce7;
    border-color: #22c55e;
  }
}

/* Landscape orientation optimizations for mobile */
@media (max-width: 768px) and (orientation: landscape) {
  .portal-header {
    padding: 0.5rem 1rem;
  }

  .portal-title h1 {
    font-size: 1.25rem;
  }

  .portal-title p {
    font-size: 0.8rem;
  }

  .chat-input.portal-mode {
    padding: 0.75rem 1rem;
  }

  .chat-messages.portal-mode {
    padding: 0.75rem 1rem;
  }
}

/* High DPI display optimizations */
@media (-webkit-min-device-pixel-ratio: 2), (min-resolution: 192dpi) {
  .chat-input.portal-mode .icon-button {
    border-width: 0.5px;
  }
}

/* Focus management for accessibility */
.chat-input.portal-mode .portal-input:focus,
.chat-input.portal-mode .portal-textarea:focus {
  outline: none; /* We have custom focus styles */
}

.chat-input.portal-mode .icon-button:focus {
  outline: 2px solid #667eea;
  outline-offset: 2px;
}

/* Smooth textarea height transitions */
.chat-input.portal-mode .portal-textarea {
  transition: height 0.2s ease-out, border-color 0.3s ease, box-shadow 0.3s ease;
}

/* Placeholder styling for textarea */
.chat-input.portal-mode .portal-textarea::placeholder {
  color: #9ca3af;
  opacity: 1;
}

/* Reduce motion for users who prefer it */
@media (prefers-reduced-motion: reduce) {
  .chat-input.portal-mode .icon-button {
    transition: none;
  }

  .chat-input.portal-mode .portal-input,
  .chat-input.portal-mode .portal-textarea {
    transition: none;
  }
}
