<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Portal Development - QuoteAI</title>
    <style>
        /* Loading screen styles */
        .loading-screen {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            display: flex;
            align-items: center;
            justify-content: center;
            z-index: 9999;
            transition: opacity 0.5s ease-out;
        }
        
        .loading-content {
            text-align: center;
            color: white;
        }
        
        .loading-spinner {
            width: 50px;
            height: 50px;
            border: 4px solid rgba(255, 255, 255, 0.3);
            border-top: 4px solid white;
            border-radius: 50%;
            animation: spin 1s linear infinite;
            margin: 0 auto 1rem auto;
        }
        
        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }
        
        .loading-text {
            font-size: 1.2rem;
            font-weight: 500;
            margin-bottom: 0.5rem;
        }
        
        .loading-subtext {
            font-size: 0.9rem;
            opacity: 0.8;
        }
        
        /* Hide loading screen when portal loads */
        .portal-loaded .loading-screen {
            opacity: 0;
            pointer-events: none;
        }
        
        /* Dev info banner */
        .dev-banner {
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            background: #f59e0b;
            color: white;
            padding: 0.5rem;
            text-align: center;
            font-size: 0.875rem;
            font-weight: 500;
            z-index: 10000;
            box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
        }
        
        .dev-banner a {
            color: white;
            text-decoration: underline;
            margin-left: 1rem;
        }
        
        /* Adjust portal container for dev banner */
        #portal-root {
            margin-top: 40px;
            height: calc(100vh - 40px);
        }
    </style>
</head>
<body>
    <!-- Development Banner -->
    <div class="dev-banner">
        🚧 Development Mode - Portal Testing
        <a href="/portal-test.html" target="_blank">Open Test Environment</a>
    </div>
    
    <!-- Loading Screen -->
    <div class="loading-screen" id="loading-screen">
        <div class="loading-content">
            <div class="loading-spinner"></div>
            <div class="loading-text">Loading Portal...</div>
            <div class="loading-subtext" id="loading-tradie">Connecting...</div>
        </div>
    </div>
    
    <!-- Portal Root -->
    <div id="portal-root"></div>
    
    <!-- Error Fallback -->
    <noscript>
        <div style="
            display: flex;
            align-items: center;
            justify-content: center;
            height: 100vh;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', sans-serif;
            margin-top: 40px;
        ">
            <div style="
                text-align: center;
                background: white;
                padding: 3rem;
                border-radius: 20px;
                box-shadow: 0 20px 40px rgba(0, 0, 0, 0.2);
                max-width: 500px;
                margin: 1rem;
            ">
                <div style="font-size: 4rem; margin-bottom: 1rem;">⚠️</div>
                <h1 style="color: #374151; margin-bottom: 1rem;">JavaScript Required</h1>
                <p style="color: #6b7280; margin-bottom: 1rem; line-height: 1.6;">
                    This portal requires JavaScript to function properly. 
                    Please enable JavaScript in your browser and refresh the page.
                </p>
            </div>
        </div>
    </noscript>
    
    <!-- React Dependencies -->
    <script crossorigin src="https://unpkg.com/react@18/umd/react.development.js"></script>
    <script crossorigin src="https://unpkg.com/react-dom@18/umd/react-dom.development.js"></script>
    
    <!-- Portal Script - Development Mode -->
    <script type="module" src="/src/portal.tsx"></script>
    
    <script>
        // Extract tradie name from URL and update loading text
        const extractTradieFromUrl = () => {
            const path = window.location.pathname;
            const match = path.match(/\/chat\/([^\/]+)/);
            return match ? decodeURIComponent(match[1]) : null;
        };
        
        // Update loading text with tradie name
        const tradieName = extractTradieFromUrl();
        if (tradieName) {
            document.getElementById('loading-tradie').textContent = `Connecting to ${tradieName}...`;
        }
        
        // Hide loading screen once portal loads
        window.addEventListener('load', () => {
            setTimeout(() => {
                document.body.classList.add('portal-loaded');
                setTimeout(() => {
                    const loadingScreen = document.getElementById('loading-screen');
                    if (loadingScreen) {
                        loadingScreen.remove();
                    }
                }, 500);
            }, 1500); // Longer delay for development to see loading screen
        });
        
        // Error handling
        window.addEventListener('error', (event) => {
            console.error('Portal loading error:', event.error);
            const loadingScreen = document.getElementById('loading-screen');
            if (loadingScreen) {
                loadingScreen.innerHTML = `
                    <div class="loading-content">
                        <div style="font-size: 4rem; margin-bottom: 1rem;">⚠️</div>
                        <div class="loading-text">Development Error</div>
                        <div class="loading-subtext">${event.error?.message || 'Unknown error occurred'}</div>
                        <button onclick="window.location.reload()" style="
                            background: white;
                            color: #667eea;
                            border: 2px solid white;
                            padding: 0.75rem 2rem;
                            border-radius: 12px;
                            font-weight: 600;
                            cursor: pointer;
                            font-size: 1rem;
                            margin-top: 1rem;
                        ">
                            Reload Portal
                        </button>
                        <a href="/portal-test.html" style="
                            display: inline-block;
                            color: white;
                            text-decoration: underline;
                            margin-top: 1rem;
                            margin-left: 1rem;
                        ">
                            Back to Test Environment
                        </a>
                    </div>
                `;
            }
        });
        
        // Development helpers
        if (window.location.hostname === 'localhost') {
            console.log('🚧 Portal Development Mode');
            console.log('Tradie Name:', tradieName || 'Not found in URL');
            console.log('URL Path:', window.location.pathname);
            
            // Add development keyboard shortcuts
            document.addEventListener('keydown', (e) => {
                if (e.ctrlKey || e.metaKey) {
                    switch (e.key) {
                        case 'r':
                            // Ctrl/Cmd + R - Reload
                            break;
                        case 't':
                            // Ctrl/Cmd + T - Open test environment
                            e.preventDefault();
                            window.open('/portal-test.html', '_blank');
                            break;
                        case 'd':
                            // Ctrl/Cmd + D - Toggle debug info
                            e.preventDefault();
                            console.log('Debug Info:', {
                                tradieName,
                                url: window.location.href,
                                config: window.QUOTE_AI_CONFIG
                            });
                            break;
                    }
                }
            });
        }
    </script>
</body>
</html>
